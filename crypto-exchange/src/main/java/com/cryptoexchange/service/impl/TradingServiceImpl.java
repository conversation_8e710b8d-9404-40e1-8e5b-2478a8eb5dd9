package com.cryptoexchange.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cryptoexchange.common.PageResult;
import com.cryptoexchange.common.Result;
import com.cryptoexchange.common.ResultCode;
import com.cryptoexchange.dto.request.CreateOrderRequest;
import com.cryptoexchange.dto.request.CancelOrderRequest;
import com.cryptoexchange.dto.request.TradeQueryRequest;
import com.cryptoexchange.dto.response.OrderResponse;
import com.cryptoexchange.dto.response.TradeResponse;
import com.cryptoexchange.dto.response.MarketDepthResponse;
import com.cryptoexchange.dto.response.KlineResponse;
import com.cryptoexchange.dto.response.PositionResponse;
import com.cryptoexchange.dto.response.AssetSnapshotResponse;
import com.cryptoexchange.dto.response.TradingStatisticsResponse;
import com.cryptoexchange.dto.response.OrderBookResponse;
import com.cryptoexchange.dto.response.OrderTestResponse;
import com.cryptoexchange.dto.response.TradingFeeResponse;
import com.cryptoexchange.dto.request.KlineQueryRequest;
import com.cryptoexchange.dto.request.AssetSnapshotQueryRequest;
import com.cryptoexchange.entity.*;
import com.cryptoexchange.exception.BusinessException;
import com.cryptoexchange.mapper.*;
import com.cryptoexchange.service.TradingService;
import com.cryptoexchange.service.WalletService;
import com.cryptoexchange.service.NotificationService;
import com.cryptoexchange.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * 交易服务实现类
 */
@Slf4j
@Service
public class TradingServiceImpl implements TradingService {

    @Override
    public Result<Void> getAccount(Long userId) {
        // TODO: 实现获取用户账户信息逻辑
        log.info("获取用户账户信息，用户ID: {}", userId);
        return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "方法待实现");
    }
    
    @Autowired
    private OrderMapper orderMapper;
    
    @Autowired
    private TradeMapper tradeMapper;
    
    @Autowired
    private UserWalletMapper userWalletMapper;
    
    @Autowired
    private TradingPairMapper tradingPairMapper;
    
    @Autowired
    private WalletService walletService;
    
    @Autowired
    private NotificationService notificationService;
    
    @Autowired
    private RedisUtil redisUtil;
    
    // 交易对锁，防止并发撮合
    private final Map<String, ReentrantLock> tradingPairLocks = new HashMap<>();
    
    private static final String ORDER_BOOK_KEY_PREFIX = "orderbook:";
    private static final String MARKET_DEPTH_KEY_PREFIX = "depth:";
    private static final String TICKER_24HR_KEY_PREFIX = "ticker24hr:";
    private static final String KLINE_KEY_PREFIX = "kline:";
    
    @Override
    @Transactional
    public Result<OrderResponse> createOrder(CreateOrderRequest request) {
        log.info("创建订单请求: {}", request);
        
        try {
            // 1. 参数验证
            if (!validateCreateOrderRequest(request)) {
                return Result.error(ResultCode.PARAM_ERROR.getCode(), "订单参数验证失败");
            }
            
            // 2. 获取交易对信息
            TradingPair tradingPair = tradingPairMapper.selectOne(
                new QueryWrapper<TradingPair>().eq("symbol", request.getSymbol())
            );
            if (tradingPair == null || tradingPair.getStatus() != 1) {
                return Result.error(ResultCode.TRADING_PAIR_NOT_FOUND.getCode(), "交易对不存在或已停用");
            }
            
            // 3. 创建订单对象
            Order order = new Order();
            order.setOrderNo(generateOrderNo());
            order.setUserId(request.getUserId());
            order.setTradingPairId(tradingPair.getId());
            order.setSymbol(request.getSymbol());
            // 转换订单类型
            Integer orderTypeInt = "MARKET".equals(request.getOrderType()) ? 2 : 1;
            order.setOrderType(orderTypeInt);
            
            // 转换买卖方向
            Integer sideInt = "BUY".equals(request.getSide()) ? 1 : 2;
            order.setSide(sideInt);
            order.setPrice(request.getPrice());
            order.setQuantity(request.getQuantity());
            order.setAmount(request.getPrice().multiply(request.getQuantity()));
            order.setFilledQuantity(BigDecimal.ZERO);
            order.setFilledAmount(BigDecimal.ZERO);
            order.setRemainingQuantity(request.getQuantity());
            order.setStatus(1); // 待成交
            order.setCreateTime(LocalDateTime.now());
            order.setUpdateTime(LocalDateTime.now());
            
            // 4. 验证订单合法性
            if (!validateOrder(order)) {
                return Result.error(ResultCode.ERROR.getCode(), "订单验证失败");
            }
            
            // 5. 检查用户资产
            String currency = request.getSide() == 1 ? 
                tradingPair.getQuoteCurrency() : tradingPair.getBaseCurrency();
            BigDecimal requiredAmount = request.getSide() == 1 ? 
                order.getAmount() : order.getQuantity();
                
            if (!checkUserBalance(request.getUserId(), currency, requiredAmount)) {
                return Result.error(ResultCode.INSUFFICIENT_BALANCE.getCode(), "余额不足");
            }
            
            // 6. 冻结用户资产
            try {
                freezeUserAsset(request.getUserId(), currency, requiredAmount);
            } catch (Exception e) {
                return Result.error(ResultCode.ERROR.getCode(), "资产冻结失败");
            }
            
            // 7. 保存订单
            orderMapper.insert(order);
            
            // 8. 添加到订单簿
            addToOrderBook(order);
            
            // 9. 尝试撮合
            executeMatching(request.getSymbol());
            
            // 10. 构造响应
            OrderResponse response = new OrderResponse();
            BeanUtils.copyProperties(order, response);
            
            log.info("订单创建成功: {}", order.getOrderNo());
            return Result.success(response);
            
        } catch (Exception e) {
            log.error("创建订单失败", e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), "创建订单失败");
        }
    }
    
    @Override
    @Transactional
    public Result<Void> cancelOrder(CancelOrderRequest request) {
        log.info("取消订单请求: {}", request);
        
        try {
            // 1. 查询订单
            Order order = orderMapper.selectOne(
                new QueryWrapper<Order>()
                    .eq("id", request.getOrderId())
                    .eq("user_id", request.getUserId())
            );
            
            if (order == null) {
                return Result.error(ResultCode.NOT_FOUND.getCode(), "订单不存在");
            }
            
            if (order.getStatus() != 1) {
                return Result.error(ResultCode.ERROR.getCode(), "订单状态不允许取消");
            }
            
            // 2. 更新订单状态
            order.setStatus(3); // 已取消
            order.setUpdateTime(LocalDateTime.now());
            orderMapper.updateById(order);
            
            // 3. 从订单簿移除
            removeFromOrderBook(order);
            
            // 4. 解冻用户资产
            TradingPair tradingPair = tradingPairMapper.selectById(order.getTradingPairId());
            String currency = order.getSide() == 1 ? 
                tradingPair.getQuoteCurrency() : tradingPair.getBaseCurrency();
            BigDecimal unfreezeAmount = order.getSide() == 1 ? 
                order.getAmount().subtract(order.getFilledAmount()) : 
                order.getRemainingQuantity();
                
            unfreezeUserAsset(order.getUserId(), currency, unfreezeAmount);
            
            log.info("订单取消成功: {}", order.getOrderNo());
            return Result.success();
            
        } catch (Exception e) {
            log.error("取消订单失败", e);
            return Result.<Void>error(ResultCode.INTERNAL_SERVER_ERROR, "取消订单失败");
        }
    }
    
    @Override
    @Transactional
    public Result<Integer> cancelAllOrders(Long userId, String symbol) {
        log.info("批量取消订单: userId={}, symbol={}", userId, symbol);
        
        try {
            QueryWrapper<Order> queryWrapper = new QueryWrapper<Order>()
                .eq("user_id", userId)
                .eq("status", 1); // 待成交状态
                
            if (StringUtils.hasText(symbol)) {
                queryWrapper.eq("symbol", symbol);
            }
            
            List<Order> orders = orderMapper.selectList(queryWrapper);
            
            int cancelCount = 0;
            for (Order order : orders) {
                CancelOrderRequest cancelRequest = new CancelOrderRequest();
                cancelRequest.setOrderId(order.getId());
                cancelRequest.setUserId(userId);
                
                Result<Void> result = cancelOrder(cancelRequest);
                if (result.isSuccess()) {
                    cancelCount++;
                }
            }
            
            log.info("批量取消订单完成: 取消{}个订单", cancelCount);
            return Result.success(cancelCount);
            
        } catch (Exception e) {
            log.error("批量取消订单失败", e);
            return Result.<Void>error(ResultCode.INTERNAL_SERVER_ERROR, "批量取消订单失败");
        }
    }
    
    @Override
    public Result<PageResult<OrderResponse>> getUserOrders(Long userId, String symbol, Integer status,
                                                          Integer pageNum, Integer pageSize) {
        try {
            Page<Order> page = new Page<>(pageNum, pageSize);
            QueryWrapper<Order> queryWrapper = new QueryWrapper<Order>()
                .eq("user_id", userId)
                .orderByDesc("create_time");
                
            if (StringUtils.hasText(symbol)) {
                queryWrapper.eq("symbol", symbol);
            }
            if (status != null) {
                queryWrapper.eq("status", status);
            }
            
            IPage<Order> orderPage = orderMapper.selectPage(page, queryWrapper);
            
            List<OrderResponse> orderResponses = orderPage.getRecords().stream()
                .map(order -> {
                    OrderResponse response = new OrderResponse();
                    BeanUtils.copyProperties(order, response);
                    return response;
                })
                .collect(Collectors.toList());
                
            PageResult<OrderResponse> pageResult = new PageResult<>();
            pageResult.setRecords(orderResponses);
            pageResult.setTotal(orderPage.getTotal());
            pageResult.setPages(orderPage.getPages());
            pageResult.setCurrent(orderPage.getCurrent());
            pageResult.setSize(orderPage.getSize());
            
            return Result.success(pageResult);
            
        } catch (Exception e) {
            log.error("获取用户订单列表失败", e);
            return Result.<Void>error(ResultCode.INTERNAL_SERVER_ERROR, "获取订单列表失败");
        }
    }
    
    @Override
    public OrderResponse getOrderDetail(Long userId, Long orderId) {
        try {
            Order order = orderMapper.selectOne(
                new QueryWrapper<Order>()
                    .eq("id", orderId)
                    .eq("user_id", userId)
            );
            
            if (order == null) {
                throw new RuntimeException("订单不存在");
            }
            
            OrderResponse response = new OrderResponse();
            BeanUtils.copyProperties(order, response);
            
            return response;
            
        } catch (Exception e) {
            log.error("获取订单详情失败", e);
            return Result.<Void>error(ResultCode.INTERNAL_SERVER_ERROR, "获取订单详情失败");
        }
    }
    
    @Override
    public PageResult<TradeResponse> getUserTrades(TradeQueryRequest request) {
        try {
            Page<Trade> page = new Page<>(request.getPage(), request.getSize());
            QueryWrapper<Trade> queryWrapper = new QueryWrapper<Trade>()
                .orderByDesc("create_time");
            
            // 根据用户ID过滤
            if (StringUtils.hasText(request.getUserId())) {
                Long userId = Long.valueOf(request.getUserId());
                queryWrapper.and(wrapper -> wrapper.eq("buyer_user_id", userId).or().eq("seller_user_id", userId));
            }
                
            if (StringUtils.hasText(request.getSymbol())) {
                queryWrapper.eq("symbol", request.getSymbol());
            }
            
            if (StringUtils.hasText(request.getSide())) {
                queryWrapper.eq("side", request.getSide());
            }
            
            if (request.getStartTime() != null) {
                queryWrapper.ge("create_time", request.getStartTime());
            }
            
            if (request.getEndTime() != null) {
                queryWrapper.le("create_time", request.getEndTime());
            }
            
            IPage<Trade> tradePage = tradeMapper.selectPage(page, queryWrapper);
            
            List<TradeResponse> tradeResponses = tradePage.getRecords().stream()
                .map(trade -> {
                    TradeResponse response = new TradeResponse();
                    BeanUtils.copyProperties(trade, response);
                    return response;
                })
                .collect(Collectors.toList());
                
            PageResult<TradeResponse> pageResult = new PageResult<>();
            pageResult.setRecords(tradeResponses);
            pageResult.setTotal(tradePage.getTotal());
            pageResult.setPages(tradePage.getPages());
            pageResult.setCurrent(tradePage.getCurrent());
            pageResult.setSize(tradePage.getSize());
            
            return pageResult;
            
        } catch (Exception e) {
            log.error("获取用户交易记录失败", e);
            throw new RuntimeException("获取交易记录失败", e);
        }
    }
    
    @Override
    public MarketDepthResponse getMarketDepth(String symbol, Integer limit) {
        try {
            String depthKey = MARKET_DEPTH_KEY_PREFIX + symbol;
            Object depth = redisUtil.get(depthKey);
            
            if (depth == null) {
                // 从数据库重新构建深度数据
                depth = buildMarketDepth(symbol, limit);
                redisUtil.set(depthKey, depth, 60); // 缓存1分钟
            }
            
            return (MarketDepthResponse) depth;
            
        } catch (Exception e) {
            log.error("获取市场深度失败", e);
            return new MarketDepthResponse();
        }
    }
    
    public Result<List<TradeResponse>> getLatestTrades(String symbol, Integer limit) {
        try {
            QueryWrapper<Trade> queryWrapper = new QueryWrapper<Trade>()
                .eq("symbol", symbol)
                .orderByDesc("create_time")
                .last("LIMIT " + (limit != null ? limit : 50));
                
            List<Trade> trades = tradeMapper.selectList(queryWrapper);
            
            List<TradeResponse> tradeResponses = trades.stream()
                .map(trade -> {
                    TradeResponse response = new TradeResponse();
                    BeanUtils.copyProperties(trade, response);
                    return response;
                })
                .collect(Collectors.toList());
                
            return Result.success(tradeResponses);
            
        } catch (Exception e) {
            log.error("获取最新成交记录失败", e);
            return Result.<Void>error(ResultCode.INTERNAL_SERVER_ERROR, "获取最新成交记录失败");
        }
    }
    
    @Override
    public Result<Object> get24HrTicker(String symbol) {
        try {
            String tickerKey = TICKER_24HR_KEY_PREFIX + symbol;
            Object ticker = redisUtil.get(tickerKey);
            
            if (ticker == null) {
                // 计算24小时行情数据
                ticker = calculate24HrTicker(symbol);
                redisUtil.set(tickerKey, ticker, 300); // 缓存5分钟
            }
            
            return Result.success(ticker);
            
        } catch (Exception e) {
            log.error("获取24小时行情失败", e);
            return Result.<Void>error(ResultCode.INTERNAL_SERVER_ERROR, "获取24小时行情失败");
        }
    }
    
    @Override
    public List<KlineResponse> getKlineData(String symbol, String interval, Integer limit) {
        try {
            String klineKey = KLINE_KEY_PREFIX + symbol + ":" + interval;
            Object klineData = redisUtil.get(klineKey);
            
            if (klineData == null) {
                // 从数据库获取K线数据
                klineData = buildKlineData(symbol, interval, limit);
                redisUtil.set(klineKey, klineData, 60); // 缓存1分钟
            }
            
            return (List<KlineResponse>) klineData;
            
        } catch (Exception e) {
            log.error("获取K线数据失败", e);
            return new ArrayList<>();
        }
    }
    
    @Override
    @Transactional
    public Object executeMatching(String symbol) {
        ReentrantLock lock = tradingPairLocks.computeIfAbsent(symbol, k -> new ReentrantLock());
        
        if (!lock.tryLock()) {
            return Result.success("撮合正在进行中");
        }
        
        try {
            log.debug("开始撮合: {}", symbol);
            
            // 获取买卖订单
            List<Order> buyOrders = getBuyOrders(symbol);
            List<Order> sellOrders = getSellOrders(symbol);
            
            int matchCount = 0;
            
            // 撮合逻辑
            for (Order buyOrder : buyOrders) {
                if (buyOrder.getRemainingQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }
                
                for (Order sellOrder : sellOrders) {
                    if (sellOrder.getRemainingQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                        continue;
                    }
                    
                    // 价格匹配检查
                    if (buyOrder.getPrice().compareTo(sellOrder.getPrice()) >= 0) {
                        // 执行撮合
                        BigDecimal tradePrice = sellOrder.getPrice(); // 以卖价成交
                        BigDecimal tradeQuantity = buyOrder.getRemainingQuantity()
                            .min(sellOrder.getRemainingQuantity());
                            
                        Trade trade = processTrade(buyOrder, sellOrder, tradePrice, tradeQuantity);
                        if (trade != null) {
                            matchCount++;
                        }
                        
                        // 如果买单完全成交，跳出内层循环
                        if (buyOrder.getRemainingQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                            break;
                        }
                    }
                }
            }
            
            log.debug("撮合完成: {} 成交{}笔", symbol, matchCount);
            return Result.success("撮合完成，成交" + matchCount + "笔");
            
        } catch (Exception e) {
            log.error("撮合失败: {}", symbol, e);
            return Result.<Void>error(ResultCode.INTERNAL_SERVER_ERROR, "撮合失败");
        } finally {
            lock.unlock();
        }
    }
    
    @Override
    @Transactional
    public Trade processTrade(Order buyOrder, Order sellOrder, BigDecimal tradePrice, BigDecimal tradeQuantity) {
        try {
            log.debug("处理成交: 买单={}, 卖单={}, 价格={}, 数量={}", 
                buyOrder.getOrderNo(), sellOrder.getOrderNo(), tradePrice, tradeQuantity);
            
            // 创建交易记录
            Trade trade = new Trade();
            trade.setTradeNo(generateTradeNo());
            trade.setSymbol(buyOrder.getSymbol());
            trade.setOrderId(buyOrder.getId());
            trade.setSide("BUY");
            trade.setUserId(buyOrder.getUserId());
            trade.setCounterpartyUserId(sellOrder.getUserId());
            trade.setPrice(tradePrice);
            trade.setQty(tradeQuantity);
            trade.setQuoteQty(tradePrice.multiply(tradeQuantity));
            trade.setCreateTime(LocalDateTime.now());
            
            // 保存交易记录
            tradeMapper.insert(trade);
            
            // 更新订单状态
            updateOrderAfterTrade(buyOrder, tradeQuantity, trade.getQuoteQty());
            updateOrderAfterTrade(sellOrder, tradeQuantity, trade.getQuoteQty());
            
            // 更新用户资产
            updateUserAssetsAfterTrade(trade);
            
            // 发送通知
            notificationService.sendTradeNotification(
                trade.getUserId(), 
                trade.getSymbol(), 
                trade.getSide(), 
                trade.getQuoteQty().toString(), 
                trade.getPrice().toString()
            );
            
            return trade;
            
        } catch (Exception e) {
            log.error("处理成交失败", e);
            throw new BusinessException("处理成交失败");
        }
    }
    
    @Override
    public void updateUserAsset(Long userId, String currency, BigDecimal amount, Integer type) {
        walletService.updateBalance(userId, currency, amount, type);
    }
    
    @Override
    public void freezeUserAsset(Long userId, String currency, BigDecimal amount) {
        walletService.freezeBalance(userId, currency, amount);
    }
    
    @Override
    public void unfreezeUserAsset(Long userId, String currency, BigDecimal amount) {
        walletService.unfreezeBalance(userId, currency, amount);
    }
    
    private boolean validateOrder(Order order) {
        // 检查订单类型和价格、数量的合法性
        if (order.getOrderType().equals(1)) { // 限价单
            if (order.getPrice() == null || order.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
                log.warn("订单验证失败: 限价单价格无效");
                return false;
            }
        } else if (order.getOrderType().equals(2)) { // 市价单
            // 市价单不需要价格，但需要数量
            if (order.getQuantity() == null || order.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                log.warn("订单验证失败: 市价单数量无效");
                return false;
            }
        }
        // 其他通用验证
        if (order.getQuantity() == null || order.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("订单验证失败: 数量无效");
            return false;
        }
        return true;
    }
    
    @Override
    public boolean checkUserBalance(Long userId, String currency, BigDecimal amount) {
        try {
            UserWallet wallet = userWalletMapper.selectOne(
                new QueryWrapper<UserWallet>()
                    .eq("user_id", userId)
                    .eq("currency", currency)
                    .eq("wallet_type", 1) // 现货钱包
            );
            
            return wallet != null && wallet.getAvailableBalance().compareTo(amount) >= 0;
            
        } catch (Exception e) {
            log.error("检查用户余额失败", e);
            return false;
        }
    }
    
    // 私有方法
    
    private boolean validateCreateOrderRequest(CreateOrderRequest request) {
        if (request.getUserId() == null) {
            log.warn("创建订单请求验证失败: 用户ID无效");
            return false;
        }
        if (StringUtils.isEmpty(request.getSymbol())) {
            log.warn("创建订单请求验证失败: 交易对为空");
            return false;
        }
        if (request.getOrderType() == null) {
            log.warn("创建订单请求验证失败: 订单类型无效");
            return false;
        }
        if (request.getSide() == null || (request.getSide() != 1 && request.getSide() != 2)) {
            log.warn("创建订单请求验证失败: 订单方向无效");
            return false;
        }
        if (request.getPrice() == null || request.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("创建订单请求验证失败: 价格无效");
            return false;
        }
        if (request.getQuantity() == null || request.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("创建订单请求验证失败: 数量无效");
            return false;
        }
        return true;
    }
    
    private String generateOrderNo() {
        return "ORD" + System.currentTimeMillis() + String.format("%04d", new Random().nextInt(10000));
    }
    
    private String generateTradeNo() {
        return "TRD" + System.currentTimeMillis() + String.format("%04d", new Random().nextInt(10000));
    }
    
    private void addToOrderBook(Order order) {
        // 添加订单到Redis订单簿
        String orderBookKey = ORDER_BOOK_KEY_PREFIX + order.getSymbol() + ":" + 
            (order.getSide() == 1 ? "buy" : "sell");
        redisUtil.zadd(orderBookKey, order.getPrice().doubleValue(), order.getId().toString());
    }
    
    private void removeFromOrderBook(Order order) {
        // 从Redis订单簿移除订单
        String orderBookKey = ORDER_BOOK_KEY_PREFIX + order.getSymbol() + ":" + 
            (order.getSide() == 1 ? "buy" : "sell");
        redisUtil.zrem(orderBookKey, order.getId().toString());
    }
    
    private List<Order> getBuyOrders(String symbol) {
        return orderMapper.selectList(
            new QueryWrapper<Order>()
                .eq("symbol", symbol)
                .eq("side", 1)
                .eq("status", 1)
                .gt("remaining_quantity", 0)
                .orderByDesc("price")
                .orderByAsc("create_time")
                .last("LIMIT 100")
        );
    }
    
    private List<Order> getSellOrders(String symbol) {
        return orderMapper.selectList(
            new QueryWrapper<Order>()
                .eq("symbol", symbol)
                .eq("side", 2)
                .eq("status", 1)
                .gt("remaining_quantity", 0)
                .orderByAsc("price")
                .orderByAsc("create_time")
                .last("LIMIT 100")
        );
    }
    
    private void updateOrderAfterTrade(Order order, BigDecimal tradeQuantity, BigDecimal tradeAmount) {
        order.setFilledQuantity(order.getFilledQuantity().add(tradeQuantity));
        order.setFilledAmount(order.getFilledAmount().add(tradeAmount));
        order.setRemainingQuantity(order.getQuantity().subtract(order.getFilledQuantity()));
        
        // 判断订单是否完全成交
        if (order.getRemainingQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            order.setStatus(2); // 已成交
            removeFromOrderBook(order);
        }
        
        order.setUpdateTime(LocalDateTime.now());
        orderMapper.updateById(order);
    }
    
    private void updateUserAssetsAfterTrade(Trade trade) {
        // 获取交易对信息
        TradingPair tradingPair = tradingPairMapper.selectById(trade.getTradingPairId());
        
        // 买方：减少计价币种，增加基础币种
        updateUserAsset(trade.getBuyerUserId(), tradingPair.getQuoteCurrency(), 
            trade.getAmount().negate(), 1);
        updateUserAsset(trade.getBuyerUserId(), tradingPair.getBaseCurrency(), 
            trade.getQuantity(), 1);
            
        // 卖方：减少基础币种，增加计价币种
        updateUserAsset(trade.getSellerUserId(), tradingPair.getBaseCurrency(), 
            trade.getQuantity().negate(), 1);
        updateUserAsset(trade.getSellerUserId(), tradingPair.getQuoteCurrency(), 
            trade.getAmount(), 1);
    }
    
    private Object buildMarketDepth(String symbol, Integer limit) {
        // TODO: 实现市场深度构建逻辑
        Map<String, Object> depth = new HashMap<>();
        depth.put("symbol", symbol);
        depth.put("bids", new ArrayList<>());
        depth.put("asks", new ArrayList<>());
        return depth;
    }
    
    private Object calculate24HrTicker(String symbol) {
        // TODO: 实现24小时行情计算逻辑
        Map<String, Object> ticker = new HashMap<>();
        ticker.put("symbol", symbol);
        ticker.put("priceChange", "0");
        ticker.put("priceChangePercent", "0");
        ticker.put("volume", "0");
        ticker.put("count", 0);
        return ticker;
    }
    
    private Object buildKlineData(String symbol, String interval, Integer limit) {
        // TODO: 实现K线数据构建逻辑
        return new ArrayList<>();
    }

    @Override
    public List<KlineResponse> getKlines(KlineQueryRequest request) {
        // TODO: 实现获取K线数据逻辑
        log.info("获取K线数据，请求参数: {}", request);
        throw new UnsupportedOperationException("方法待实现");
    }

    @Override
    public List<PositionResponse> getPositions(Long userId, String symbol) {
        // TODO: 实现获取持仓信息逻辑
        log.info("获取持仓信息，用户ID: {}, 交易对: {}", userId, symbol);
        throw new UnsupportedOperationException("方法待实现");
    }

    @Override
    public AssetSnapshotResponse getAssetSnapshot(AssetSnapshotQueryRequest request) {
        // TODO: 实现获取资产快照逻辑
        log.info("获取资产快照，请求参数: {}", request);
        throw new UnsupportedOperationException("方法待实现");
    }

    @Override
    public TradingStatisticsResponse getTradingStatistics(Long userId, String symbol) {
        // TODO: 实现获取交易统计逻辑
        log.info("获取交易统计，用户ID: {}, 交易对: {}", userId, symbol);
        throw new UnsupportedOperationException("方法待实现");
    }

    @Override
    public OrderBookResponse getOrderBook(String symbol) {
        // TODO: 实现获取订单簿逻辑
        log.info("获取订单簿，交易对: {}", symbol);
        throw new UnsupportedOperationException("方法待实现");
    }

    @Override
    public TradingFeeResponse getTradingFee(Long userId, String symbol) {
        log.info("获取交易手续费: userId={}, symbol={}", userId, symbol);
        
        try {
            // TODO: 实现交易手续费计算逻辑
            TradingFeeResponse response = new TradingFeeResponse();
            response.setSymbol(symbol);
            response.setMakerFee("0.001");
            response.setTakerFee("0.001");
            response.setVipLevel(0);
            
            return response;
            
        } catch (Exception e) {
            log.error("获取交易手续费失败", e);
            throw new RuntimeException("获取交易手续费失败: " + e.getMessage());
        }
    }

    @Override
    public OrderTestResponse testOrder(Long userId, CreateOrderRequest request) {
        log.info("测试订单: userId={}, request={}", userId, request);
        
        try {
            // 1. 参数验证
            if (!validateCreateOrderRequest(request)) {
                OrderTestResponse response = new OrderTestResponse();
                response.setSuccess(false);
                response.setMessage("订单参数验证失败");
                return response;
            }
            
            // 2. 获取交易对信息
            TradingPair tradingPair = tradingPairMapper.selectOne(
                new QueryWrapper<TradingPair>().eq("symbol", request.getSymbol())
            );
            if (tradingPair == null || tradingPair.getStatus() != 1) {
                OrderTestResponse response = new OrderTestResponse();
                response.setStatus("FAILED");
                response.setErrorMessage("交易对不存在或已停用");
                return response;
            }
            
            // 3. 检查用户资产
            String currency = "BUY".equals(request.getSide()) ? 
                tradingPair.getQuoteCurrency() : tradingPair.getBaseCurrency();
            BigDecimal requiredAmount = "BUY".equals(request.getSide()) ? 
                request.getPrice().multiply(request.getQuantity()) : request.getQuantity();
                
            if (!checkUserBalance(userId, currency, requiredAmount)) {
                OrderTestResponse response = new OrderTestResponse();
                response.setStatus("FAILED");
                response.setErrorMessage("余额不足");
                return response;
            }
            
            // 4. 构造成功响应
            OrderTestResponse response = new OrderTestResponse();
            response.setStatus("SUCCESS");
            response.setSymbol(request.getSymbol());
            response.setSide(request.getSide());
            response.setOrderType(request.getOrderType());
            response.setPrice(request.getPrice());
            response.setQuantity(request.getQuantity());
            response.setSufficientBalance(true);
            
            return response;
            
        } catch (Exception e) {
            log.error("测试订单失败", e);
            OrderTestResponse response = new OrderTestResponse();
            response.setStatus("FAILED");
            response.setErrorMessage("测试订单失败: " + e.getMessage());
            return response;
        }
    }
}